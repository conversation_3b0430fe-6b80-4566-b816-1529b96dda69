<template>
  <div id="top-header">
    <!-- 左侧：日期和星期 -->
    <div class="header-left">
      <div class="date-info">
        <div class="current-date">{{ currentDate }}</div>
        <div class="current-week">{{ currentWeek }}</div>
      </div>
    </div>

    <!-- 中间：标题 -->
    <div class="header-center">
      <div class="center-title">巴音郭楞职业技术学院门禁管理大屏</div>
    </div>

    <!-- 右侧：天气 -->
    <div class="header-right">
      <div class="weather-info">
        <div class="weather-icon">
          <i :class="weatherIcon"></i>
        </div>
        <div class="weather-details">
          <div class="temperature">{{ temperature }}°C</div>
          <div class="weather-desc">{{ weatherDesc }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TopHeader',
  data () {
    return {
      currentDate: '',
      currentWeek: '',
      temperature: '--',
      weatherDesc: '晴',
      weatherIcon: 'icon-sunny'
    }
  },
  mounted () {
    this.updateDateTime()
    this.getWeatherInfo()
    // 每分钟更新一次时间
    this.timer = setInterval(() => {
      this.updateDateTime()
    }, 60000)
  },
  beforeDestroy () {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    updateDateTime () {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')

      this.currentDate = `${year}-${month}-${day}`

      const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.currentWeek = weekDays[now.getDay()]
    },

    async getWeatherInfo () {
      try {
        // 这里可以调用天气API获取实际天气数据
        // 暂时使用模拟数据
        this.temperature = '25'
        this.weatherDesc = '晴'
        this.weatherIcon = 'icon-sunny'
      } catch (error) {
        console.error('获取天气信息失败:', error)
        this.temperature = '--'
        this.weatherDesc = '未知'
        this.weatherIcon = 'icon-unknown'
      }
    }
  }
}
</script>

<style lang="less">
#top-header {
  position: relative;
  width: 100%;
  height: 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  background: url('./img/head.webp') no-repeat;
  background-size: 100% 100%;
  margin-bottom: 15px;
  padding: 0 50px;

  // 左侧日期区域
  .header-left {
    flex: 1;
    display: flex;
    align-items: center;
    min-width: 200px;

    .date-info {
      color: #ffffff;
      display: flex;
      align-items: center;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      background: rgba(0, 0, 0, 0.1);
      padding: 8px 12px;
      border-radius: 6px;
      backdrop-filter: blur(5px);
      .current-date {
       font-size: 16px;
        opacity: 0.9;
      }

      .current-week {
        margin-left: 1rem;
        font-size: 16px;
        opacity: 0.9;
      }
    }
  }

  // 中间标题区域
  .header-center {
    flex: 1.5;
    display: flex;
    justify-content: center;
    align-items: center;

    .center-title {
      font-size: 22px;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      text-align: center;
    }
  }

  // 右侧天气区域
  .header-right {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-right: 3rem;
    min-width: 200px;

    .weather-info {
      display: flex;
      align-items: center;
      color: #ffffff;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      padding: 8px 12px;
      .weather-icon {
        margin-right: 8px;
        font-size: 18px;

        i {
          &.icon-sunny::before {
            content: '☀️';
          }
          &.icon-cloudy::before {
            content: '☁️';
          }
          &.icon-rainy::before {
            content: '🌧️';
          }
          &.icon-snowy::before {
            content: '❄️';
          }
          &.icon-unknown::before {
            content: '🌤️';
          }
        }
      }

      .weather-details {
        .temperature {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 2px;
        }

        .weather-desc {
          font-size: 12px;
          opacity: 0.9;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .header-left .date-info .current-date {
      font-size: 16px;
    }

    .header-center .center-title {
      font-size: 20px;
    }

    .header-right .weather-info .temperature {
      font-size: 14px;
    }
  }
}
</style>
